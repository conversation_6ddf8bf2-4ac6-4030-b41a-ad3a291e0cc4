Windows 11 一键脚本（管理员 PowerShell 打开后复制即可）：
# 设置两条用户级变量（重装后运行一次就够了）
setx ANTHROPIC_BASE_URL   "http://localhost:3000"
setx ANTHROPIC_AUTH_TOKEN "123456"

# 启动命令
node src/api-server.js ^
  --model-provider claude-kiro-oauth ^
  --port 3000 ^
  --required-api-key 123456 ^
  --kiro-oauth-creds-file "C:\Users\<USER>\.aws\sso\cache\kiro-auth-token.json"

#健康检查
irm -Uri http://localhost:3000/health -Headers @{Authorization="Bearer 123456"}