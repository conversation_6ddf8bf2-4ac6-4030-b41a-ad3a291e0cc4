{"type": "module", "dependencies": {"axios": "^1.10.0", "dotenv": "^16.4.5", "google-auth-library": "^10.1.0", "lodash": "^4.17.21", "undici": "^7.12.0", "uuid": "^11.1.0", "deepmerge": "^4.3.1"}, "devDependencies": {"@babel/preset-env": "^7.28.0", "@jest/globals": "^29.7.0", "babel-jest": "^30.0.5", "babel-plugin-transform-import-meta": "^2.3.3", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "supertest": "^6.3.3"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "test:silent": "jest --silent", "test:unit": "node run-tests.js --unit", "test:integration": "node run-tests.js --integration", "test:summary": "node test-summary.js"}}